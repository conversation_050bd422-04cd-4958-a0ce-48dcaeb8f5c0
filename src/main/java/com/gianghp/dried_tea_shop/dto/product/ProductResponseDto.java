package com.gianghp.dried_tea_shop.dto.product;

import com.gianghp.dried_tea_shop.dto.common.ImageDto;
import com.gianghp.dried_tea_shop.enums.ProductStatus;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductResponseDto {
    private String productId;
    private String name;
    private String description;
    private BigDecimal price;
    private Integer stockQuantity;
    private String categoryName;
    private BigDecimal discountValue;
    private ImageDto image;
    private BigDecimal rating;
    private Integer reviewCount;
    private ProductStatus status;
    private String createdAt;
}
