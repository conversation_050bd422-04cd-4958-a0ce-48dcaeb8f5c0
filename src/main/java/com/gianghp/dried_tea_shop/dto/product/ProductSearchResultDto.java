package com.gianghp.dried_tea_shop.dto.product;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
public class ProductSearchResultDto {
  private UUID productId;
  private String name;
  private String description;
  private BigDecimal price;
  private Integer reviewCount;
  private BigDecimal rating;
  private String categoryName;

  private Double simName;
  private Double rankName;
  private Double rankDesc;
  private Double rankCat;
  private Double rankCatDesc;
}
