package com.gianghp.dried_tea_shop.dto.product;

import com.gianghp.dried_tea_shop.enums.ProductStatus;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductRequestDto {
    private String name;
    private String description;
    private BigDecimal price;
    private Integer stockQuantity;
    private String categoryId;
    private String discountId;
    private String imageId;
    private ProductStatus status;
}
