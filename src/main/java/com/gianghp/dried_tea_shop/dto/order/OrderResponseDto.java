package com.gianghp.dried_tea_shop.dto.order;

import com.gianghp.dried_tea_shop.enums.OrderStatus;
import com.gianghp.dried_tea_shop.enums.PaymentMethod;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderResponseDto {
    private String orderId;
    private String userId;
    private BigDecimal totalAmount;
    private OrderStatus status;
    private PaymentMethod paymentMethod;
    private String createdAt;
    private List<OrderItemResponseDto> items;
}
