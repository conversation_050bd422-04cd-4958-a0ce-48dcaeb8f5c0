package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.common.response.ApiPageResponse;
import com.gianghp.dried_tea_shop.common.response.ApiResponse;
import com.gianghp.dried_tea_shop.dto.review.ReviewRequestDto;
import com.gianghp.dried_tea_shop.dto.review.ReviewResponseDto;
import com.gianghp.dried_tea_shop.service.ReviewService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequiredArgsConstructor
@Slf4j
public class ReviewController {

    private final ReviewService reviewService;

    @GetMapping("/products/{productId}/reviews")
    public ResponseEntity<ApiPageResponse<ReviewResponseDto>> getProductReviews(
        @PathVariable UUID productId,
        @RequestParam(required = false, defaultValue = "0") Integer page,
        @RequestParam(required = false, defaultValue = "10") Integer size
    ) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ReviewResponseDto> reviews = reviewService.getReviewsByProductId(productId, pageable);
            return ResponseEntity.ok(ApiPageResponse.success(
                reviews, "Reviews retrieved successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiPageResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @PostMapping("/products/{productId}/reviews")
    public ResponseEntity<ApiResponse<ReviewResponseDto>> createReview(
        @PathVariable UUID productId,
        @RequestParam UUID userId, // In real app, this would come from authentication
        @RequestBody ReviewRequestDto reviewRequestDto
    ) {
        try {
            ReviewResponseDto review = reviewService.createReview(productId, userId, reviewRequestDto);
            return ResponseEntity.ok(ApiResponse.success(
                review, "Review created successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @PutMapping("/reviews/{id}")
    public ResponseEntity<ApiResponse<ReviewResponseDto>> updateReview(
        @PathVariable UUID id,
        @RequestBody ReviewRequestDto reviewRequestDto
    ) {
        try {
            ReviewResponseDto review = reviewService.updateReview(id, reviewRequestDto);
            return ResponseEntity.ok(ApiResponse.success(
                review, "Review updated successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }
}
