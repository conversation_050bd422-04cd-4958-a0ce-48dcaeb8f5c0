package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.common.response.ApiResponse;
import com.gianghp.dried_tea_shop.dto.cart.AddCartItemRequestDto;
import com.gianghp.dried_tea_shop.dto.cart.AddCartItemResponseDto;
import com.gianghp.dried_tea_shop.dto.cart.CartResponseDto;
import com.gianghp.dried_tea_shop.dto.cart.UpdateCartItemRequestDto;
import com.gianghp.dried_tea_shop.service.CartItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/cart")
@RequiredArgsConstructor
@Slf4j
public class CartController {

    private final CartItemService cartItemService;

    @GetMapping
    public ResponseEntity<ApiResponse<CartResponseDto>> getCart(
        @RequestParam UUID userId // In real app, this would come from authentication
    ) {
        try {
            CartResponseDto cart = cartItemService.getCartByUserId(userId);
            return ResponseEntity.ok(ApiResponse.success(
                cart, "Cart retrieved successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @PostMapping("/items")
    public ResponseEntity<ApiResponse<AddCartItemResponseDto>> addItemToCart(
        @RequestParam UUID userId, // In real app, this would come from authentication
        @RequestBody AddCartItemRequestDto addCartItemRequestDto
    ) {
        try {
            AddCartItemResponseDto cartItem = cartItemService.addItemToCart(userId, addCartItemRequestDto);
            return ResponseEntity.ok(ApiResponse.success(
                cartItem, "Item added to cart successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @PutMapping("/items/{id}")
    public ResponseEntity<ApiResponse<AddCartItemResponseDto>> updateCartItemQuantity(
        @PathVariable UUID id,
        @RequestBody UpdateCartItemRequestDto updateCartItemRequestDto
    ) {
        try {
            AddCartItemResponseDto cartItem = cartItemService.updateCartItemQuantity(id, updateCartItemRequestDto);
            return ResponseEntity.ok(ApiResponse.success(
                cartItem, "Cart item updated successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @DeleteMapping("/items/{id}")
    public ResponseEntity<ApiResponse<String>> removeCartItem(
        @PathVariable UUID id
    ) {
        try {
            cartItemService.removeCartItem(id);
            return ResponseEntity.ok(ApiResponse.success(
                null, "Cart item removed successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @DeleteMapping
    public ResponseEntity<ApiResponse<String>> clearCart(
        @RequestParam UUID userId // In real app, this would come from authentication
    ) {
        try {
            cartItemService.clearCart(userId);
            return ResponseEntity.ok(ApiResponse.success(
                null, "Cart cleared successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }
}
