package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.common.response.ApiPageResponse;
import com.gianghp.dried_tea_shop.common.response.ApiResponse;
import com.gianghp.dried_tea_shop.dto.category.CategoryRequestDto;
import com.gianghp.dried_tea_shop.dto.category.CategoryResponseDto;
import com.gianghp.dried_tea_shop.service.CategoryService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
@Slf4j
public class CategoryController {
    
    private final CategoryService categoryService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<CategoryResponseDto>>> getAllCategories() {
        try {
            return ResponseEntity.ok(ApiResponse.success(categoryService.getAllCategories()));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }


    @PostMapping
    public ResponseEntity<ApiResponse<CategoryResponseDto>> createCategory(
        @RequestBody CategoryRequestDto categoryRequestDto
    ) {
        try {
            CategoryResponseDto category = categoryService.createCategory(categoryRequestDto);
            return ResponseEntity.ok(ApiResponse.success(
                category, "Category created successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<CategoryResponseDto>> updateCategory(
        @PathVariable UUID id,
        @RequestBody CategoryRequestDto categoryRequestDto
    ) {
        try {
            CategoryResponseDto category = categoryService.updateCategory(id, categoryRequestDto);
            return ResponseEntity.ok(ApiResponse.success(
                category, "Category updated successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }
}
