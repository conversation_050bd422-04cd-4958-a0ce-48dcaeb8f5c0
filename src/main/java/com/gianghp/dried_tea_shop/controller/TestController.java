package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.common.response.ApiResponse;
import com.gianghp.dried_tea_shop.dto.product.ProductResponseDto;
import com.gianghp.dried_tea_shop.tool.ProductAgentTool;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Test controller để demo các thành phần đã tạo
 */
@Slf4j
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
public class TestController {

  private final ProductAgentTool tool;

  @GetMapping("/tool/getProductList")
  public ResponseEntity<ApiResponse<List<ProductResponseDto>>> getProductListTool(
      @RequestParam(required = false) String keyword,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) BigDecimal minPrice,
      @RequestParam(required = false) BigDecimal maxPrice,
      @RequestParam(required = false) Integer minReview,
      @RequestParam(required = false) UUID categoryId,
      @RequestParam(required = false, defaultValue = "10") int limit

  ) {
    try {
      return ResponseEntity.ok(
          ApiResponse.success(tool.getProductList(
                  keyword, status, minPrice, maxPrice, minReview, categoryId, limit
              )
          )
      );
    } catch (Exception e) {
      log.error("Error occurred: {}", e.getMessage(), e);
      return ResponseEntity.badRequest()
          .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
    }
  }

}
