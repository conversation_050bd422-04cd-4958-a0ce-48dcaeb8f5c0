package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.common.response.ApiPageResponse;
import com.gianghp.dried_tea_shop.common.response.ApiResponse;
import com.gianghp.dried_tea_shop.dto.product.ProductRequestDto;
import com.gianghp.dried_tea_shop.dto.product.ProductResponseDto;
import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.service.ProductService;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/products")
@RequiredArgsConstructor
@Slf4j
public class ProductController {

  private final ProductService productService;

  @GetMapping
  public ResponseEntity<ApiPageResponse<ProductResponseDto>> getAllProducts(
      @RequestParam(required = false) String name,
      @RequestParam(required = false) String categoryId,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) BigDecimal minPrice,
      @RequestParam(required = false) BigDecimal maxPrice,
      @RequestParam(required = false) BigDecimal minRating,
      @RequestParam(required = false, defaultValue = "0") Integer page,
      @RequestParam(required = false, defaultValue = "10") Integer size,
      @RequestParam(required = false, defaultValue = "createdAt") String sortBy
  ) {
    try {
      Page<ProductResponseDto> products = productService.getAllProducts(
          name, categoryId, status, minPrice, maxPrice, minRating, page, size, sortBy
      );
      return ResponseEntity.ok(ApiPageResponse.success(
          products, "Products retrieved successfully"
      ));
    } catch (Exception e) {
      log.error("Error occurred: {}", e.getMessage(), e);
      return ResponseEntity.badRequest()
          .body(ApiPageResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
    }
  }

  @GetMapping("/{id}")
  public ResponseEntity<ApiResponse<ProductResponseDto>> getProductById(
      @PathVariable UUID id
  ) {
    try {
      ProductResponseDto product = productService.getProductById(id);
      return ResponseEntity.ok(ApiResponse.success(
          product, "Product retrieved successfully"
      ));
    } catch (Exception e) {
      log.error("Error occurred: {}", e.getMessage(), e);
      return ResponseEntity.badRequest()
          .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
    }
  }

  @PostMapping
  public ResponseEntity<ApiResponse<ProductResponseDto>> createProduct(
      @RequestBody ProductRequestDto productRequestDto
  ) {
    try {
      ProductResponseDto product = productService.createProduct(productRequestDto);
      return ResponseEntity.ok(ApiResponse.success(
          product, "Product created successfully"
      ));
    } catch (Exception e) {
      log.error("Error occurred: {}", e.getMessage(), e);
      return ResponseEntity.badRequest()
          .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
    }
  }

  @PutMapping("/{id}")
  public ResponseEntity<ApiResponse<ProductResponseDto>> updateProduct(
      @PathVariable UUID id,
      @RequestBody ProductRequestDto productRequestDto
  ) {
    try {
      ProductResponseDto product = productService.updateProduct(id, productRequestDto);
      return ResponseEntity.ok(ApiResponse.success(
          product, "Product updated successfully"
      ));
    } catch (Exception e) {
      log.error("Error occurred: {}", e.getMessage(), e);
      return ResponseEntity.badRequest()
          .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
    }
  }
}
