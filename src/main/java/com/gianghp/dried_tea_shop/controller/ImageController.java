package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.service.ImageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/images")
@RequiredArgsConstructor
@Slf4j
public class ImageController {
    
    private final ImageService imageService;
    
    // TODO: Implement image REST endpoints
    // POST /api/images/upload - Upload image (admin only)
    // GET /api/images/{id} - Get image by ID
    // DELETE /api/images/{id} - Delete image (admin only)
    // GET /api/images - Get all images (admin only)
    // GET /api/images/unused - Get unused images (admin only)
    // DELETE /api/images/cleanup - Clean up unused images (admin only)
    // POST /api/images/{id}/thumbnail - Generate thumbnail (admin only)
}
