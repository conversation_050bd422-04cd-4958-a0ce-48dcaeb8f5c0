package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.common.response.ApiResponse;
import com.gianghp.dried_tea_shop.dto.discount.DiscountRequestDto;
import com.gianghp.dried_tea_shop.dto.discount.DiscountResponseDto;
import com.gianghp.dried_tea_shop.service.DiscountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/discounts")
@RequiredArgsConstructor
@Slf4j
public class DiscountController {
    
    private final DiscountService discountService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<DiscountResponseDto>>> getAllDiscounts() {
        try {
            List<DiscountResponseDto> discounts = discountService.getAllDiscounts();
            return ResponseEntity.ok(ApiResponse.success(
                discounts, "Discounts retrieved successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @PostMapping
    public ResponseEntity<ApiResponse<DiscountResponseDto>> createDiscount(
        @RequestBody DiscountRequestDto discountRequestDto
    ) {
        try {
            DiscountResponseDto discount = discountService.createDiscount(discountRequestDto);
            return ResponseEntity.ok(ApiResponse.success(
                discount, "Discount created successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<DiscountResponseDto>> updateDiscount(
        @PathVariable UUID id,
        @RequestBody DiscountRequestDto discountRequestDto
    ) {
        try {
            DiscountResponseDto discount = discountService.updateDiscount(id, discountRequestDto);
            return ResponseEntity.ok(ApiResponse.success(
                discount, "Discount updated successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }
}
