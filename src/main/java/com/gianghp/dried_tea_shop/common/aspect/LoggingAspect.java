package com.gianghp.dried_tea_shop.common.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * Aspect để log request/response của controller
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class LoggingAspect {
    
    private final ObjectMapper objectMapper;
    
    /**
     * Pointcut cho tất cả các method trong controller package
     */
    @Pointcut("execution(* com.gianghp.dried_tea_shop.controller..*(..))")
    public void controllerMethods() {}
    
    /**
     * Pointcut cho tất cả các method trong service package
     */
    @Pointcut("execution(* com.gianghp.dried_tea_shop.service..*(..))")
    public void serviceMethods() {}
    
    /**
     * Log trước khi method được thực thi
     */
    @Before("controllerMethods()")
    public void logBefore(JoinPoint joinPoint) {
        try {
            HttpServletRequest request = getCurrentHttpRequest();
            if (request != null) {
                log.info("=== REQUEST START ===");
                log.info("HTTP Method: {}", request.getMethod());
                log.info("Request URL: {}", request.getRequestURL().toString());
                log.info("Request URI: {}", request.getRequestURI());
                log.info("Remote Address: {}", request.getRemoteAddr());
                log.info("User Agent: {}", request.getHeader("User-Agent"));
                log.info("Content Type: {}", request.getContentType());
                
                // Log request headers
                Map<String, String> headers = getRequestHeaders(request);
                if (!headers.isEmpty()) {
                    log.info("Request Headers: {}", toJsonString(headers));
                }
                
                // Log request parameters
                Map<String, String[]> parameters = request.getParameterMap();
                if (!parameters.isEmpty()) {
                    log.info("Request Parameters: {}", toJsonString(parameters));
                }
            }
            
            // Log method info
            log.info("Controller: {}", joinPoint.getSignature().getDeclaringTypeName());
            log.info("Method: {}", joinPoint.getSignature().getName());
            
            // Log method arguments (cẩn thận với sensitive data)
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                log.info("Method Arguments: {}", Arrays.toString(args));
            }
            
            log.info("Request Time: {}", LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("Error in logBefore: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Log sau khi method thực thi thành công
     */
    @AfterReturning(pointcut = "controllerMethods()", returning = "result")
    public void logAfterReturning(JoinPoint joinPoint, Object result) {
        try {
            log.info("=== RESPONSE SUCCESS ===");
            log.info("Controller: {}", joinPoint.getSignature().getDeclaringTypeName());
            log.info("Method: {}", joinPoint.getSignature().getName());
            
            // Log response (cẩn thận với sensitive data)
            if (result != null) {
                log.info("Response: {}", toJsonString(result));
            } else {
                log.info("Response: null");
            }
            
            log.info("Response Time: {}", LocalDateTime.now());
            log.info("=== REQUEST END ===");
            
        } catch (Exception e) {
            log.error("Error in logAfterReturning: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Log khi method ném exception
     */
    @AfterThrowing(pointcut = "controllerMethods()", throwing = "exception")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable exception) {
        try {
            log.error("=== RESPONSE ERROR ===");
            log.error("Controller: {}", joinPoint.getSignature().getDeclaringTypeName());
            log.error("Method: {}", joinPoint.getSignature().getName());
            log.error("Exception Type: {}", exception.getClass().getSimpleName());
            log.error("Exception Message: {}", exception.getMessage());
            log.error("Error Time: {}", LocalDateTime.now());
            log.error("=== REQUEST END ===");
            
        } catch (Exception e) {
            log.error("Error in logAfterThrowing: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Log execution time cho controller methods
     */
    @Around("controllerMethods()")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            long executionTime = System.currentTimeMillis() - startTime;
            
            log.info("=== EXECUTION TIME ===");
            log.info("Controller: {}", joinPoint.getSignature().getDeclaringTypeName());
            log.info("Method: {}", joinPoint.getSignature().getName());
            log.info("Execution Time: {} ms", executionTime);
            
            return result;
            
        } catch (Throwable throwable) {
            long executionTime = System.currentTimeMillis() - startTime;
            
            log.error("=== EXECUTION TIME (ERROR) ===");
            log.error("Controller: {}", joinPoint.getSignature().getDeclaringTypeName());
            log.error("Method: {}", joinPoint.getSignature().getName());
            log.error("Execution Time: {} ms", executionTime);
            
            throw throwable;
        }
    }
    
    /**
     * Log execution time cho service methods (chỉ log nếu > 1000ms)
     */
    @Around("serviceMethods()")
    public Object logSlowServiceMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            long executionTime = System.currentTimeMillis() - startTime;
            
            // Chỉ log nếu execution time > 1000ms
            if (executionTime > 1000) {
                log.warn("=== SLOW SERVICE METHOD ===");
                log.warn("Service: {}", joinPoint.getSignature().getDeclaringTypeName());
                log.warn("Method: {}", joinPoint.getSignature().getName());
                log.warn("Execution Time: {} ms", executionTime);
            }
            
            return result;
            
        } catch (Throwable throwable) {
            long executionTime = System.currentTimeMillis() - startTime;
            
            log.error("=== SERVICE METHOD ERROR ===");
            log.error("Service: {}", joinPoint.getSignature().getDeclaringTypeName());
            log.error("Method: {}", joinPoint.getSignature().getName());
            log.error("Execution Time: {} ms", executionTime);
            log.error("Error: {}", throwable.getMessage());
            
            throw throwable;
        }
    }
    
    /**
     * Lấy current HTTP request
     */
    private HttpServletRequest getCurrentHttpRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            return attributes.getRequest();
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Lấy tất cả request headers
     */
    private Map<String, String> getRequestHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            
            // Không log sensitive headers
            if (!isSensitiveHeader(headerName)) {
                headers.put(headerName, headerValue);
            } else {
                headers.put(headerName, "***MASKED***");
            }
        }
        
        return headers;
    }
    
    /**
     * Kiểm tra xem header có sensitive không
     */
    private boolean isSensitiveHeader(String headerName) {
        if (headerName == null) {
            return false;
        }
        
        String lowerCaseHeaderName = headerName.toLowerCase();
        return lowerCaseHeaderName.contains("authorization") ||
               lowerCaseHeaderName.contains("cookie") ||
               lowerCaseHeaderName.contains("token") ||
               lowerCaseHeaderName.contains("password") ||
               lowerCaseHeaderName.contains("secret");
    }
    
    /**
     * Chuyển đổi object thành JSON string
     */
    private String toJsonString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            log.warn("Cannot convert object to JSON: {}", e.getMessage());
            return obj.toString();
        }
    }
}
