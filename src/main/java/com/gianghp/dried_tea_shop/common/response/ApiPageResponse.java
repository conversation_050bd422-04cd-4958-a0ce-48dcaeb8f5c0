package com.gianghp.dried_tea_shop.common.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiPageResponse<T> {

  private boolean success;
  private String code;
  private String message;
  private List<T> data;

  // Pagination fields trực tiếp
  private int page;
  private int size;
  private long totalElements;
  private int totalPages;
  private boolean first;
  private boolean last;

  @Builder.Default
  private LocalDateTime timestamp = LocalDateTime.now();

  // ===== Success =====
  public static <T> ApiPageResponse<T> success(Page<T> page) {
    return ApiPageResponse.<T>builder()
        .success(true)
        .code(ErrorCode.SUCCESS.getCode())
        .message(ErrorCode.SUCCESS.getMessage())
        .data(page.getContent())
        .page(page.getNumber())
        .size(page.getSize())
        .totalElements(page.getTotalElements())
        .totalPages(page.getTotalPages())
        .first(page.isFirst())
        .last(page.isLast())
        .timestamp(LocalDateTime.now())
        .build();
  }

  public static <T> ApiPageResponse<T> success(Page<T> page, String message) {
    return ApiPageResponse.<T>builder()
        .success(true)
        .code(ErrorCode.SUCCESS.getCode())
        .message(message)
        .data(page.getContent())
        .page(page.getNumber())
        .size(page.getSize())
        .totalElements(page.getTotalElements())
        .totalPages(page.getTotalPages())
        .first(page.isFirst())
        .last(page.isLast())
        .timestamp(LocalDateTime.now())
        .build();
  }

  // ===== Error =====
  public static <T> ApiPageResponse<T> error(ErrorCode errorCode) {
    return ApiPageResponse.<T>builder()
        .success(false)
        .code(errorCode.getCode())
        .message(errorCode.getMessage())
        .timestamp(LocalDateTime.now())
        .build();
  }

  public static <T> ApiPageResponse<T> error(ErrorCode errorCode, String message) {
    return ApiPageResponse.<T>builder()
        .success(false)
        .code(errorCode.getCode())
        .message(message)
        .timestamp(LocalDateTime.now())
        .build();
  }

  public static <T> ApiPageResponse<T> error(String code, String message) {
    return ApiPageResponse.<T>builder()
        .success(false)
        .code(code)
        .message(message)
        .timestamp(LocalDateTime.now())
        .build();
  }
}
