package com.gianghp.dried_tea_shop.common.constants;

/**
 * Class chứa các hằng số chung của ứng dụng
 */
public final class AppConstants {
    
    // Private constructor để ngăn việc khởi tạo instance
    private AppConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
    
    // ===== PAGINATION =====
    public static final String DEFAULT_PAGE_NUMBER = "0";
    public static final String DEFAULT_PAGE_SIZE = "10";
    public static final String MAX_PAGE_SIZE = "100";
    public static final String DEFAULT_SORT_BY = "id";
    public static final String DEFAULT_SORT_DIRECTION = "asc";
    
    // ===== DATE TIME FORMATS =====
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String TIME_FORMAT = "HH:mm:ss";
    public static final String ISO_DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    
    // ===== VALIDATION =====
    public static final int MIN_PASSWORD_LENGTH = 8;
    public static final int MAX_PASSWORD_LENGTH = 100;
    public static final int MIN_USERNAME_LENGTH = 3;
    public static final int MAX_USERNAME_LENGTH = 50;
    public static final int MAX_EMAIL_LENGTH = 100;
    public static final int MAX_PHONE_LENGTH = 15;
    public static final int MAX_NAME_LENGTH = 100;
    public static final int MAX_DESCRIPTION_LENGTH = 1000;
    public static final int MAX_ADDRESS_LENGTH = 255;
    
    // ===== REGEX PATTERNS =====
    public static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
    public static final String PHONE_REGEX = "^[+]?[0-9]{10,15}$";
    public static final String USERNAME_REGEX = "^[a-zA-Z0-9_]{3,50}$";
    public static final String PASSWORD_REGEX = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$";
    
    // ===== SECURITY =====
    public static final String JWT_SECRET_KEY = "jwt.secret";
    public static final String JWT_EXPIRATION_KEY = "jwt.expiration";
    public static final long DEFAULT_JWT_EXPIRATION = 86400000L; // 24 hours in milliseconds
    public static final String AUTHORIZATION_HEADER = "Authorization";
    public static final String BEARER_PREFIX = "Bearer ";
    public static final String ROLE_PREFIX = "ROLE_";
    
    // ===== ROLES =====
    public static final String ROLE_ADMIN = "ADMIN";
    public static final String ROLE_USER = "USER";
    public static final String ROLE_MANAGER = "MANAGER";
    public static final String ROLE_EMPLOYEE = "EMPLOYEE";
    
    // ===== API ENDPOINTS =====
    public static final String API_VERSION = "/api/v1";
    public static final String AUTH_ENDPOINT = API_VERSION + "/auth";
    public static final String USER_ENDPOINT = API_VERSION + "/users";
    public static final String PRODUCT_ENDPOINT = API_VERSION + "/products";
    public static final String ORDER_ENDPOINT = API_VERSION + "/orders";
    public static final String CATEGORY_ENDPOINT = API_VERSION + "/categories";
    
    // ===== HTTP HEADERS =====
    public static final String CONTENT_TYPE_JSON = "application/json";
    public static final String CONTENT_TYPE_XML = "application/xml";
    public static final String CONTENT_TYPE_FORM = "application/x-www-form-urlencoded";
    public static final String CONTENT_TYPE_MULTIPART = "multipart/form-data";
    
    // ===== CACHE =====
    public static final String CACHE_USERS = "users";
    public static final String CACHE_PRODUCTS = "products";
    public static final String CACHE_CATEGORIES = "categories";
    public static final long CACHE_TTL_SECONDS = 3600L; // 1 hour
    
    // ===== FILE UPLOAD =====
    public static final long MAX_FILE_SIZE = 10 * 1024 * 1024L; // 10MB
    public static final String[] ALLOWED_IMAGE_TYPES = {"jpg", "jpeg", "png", "gif", "webp"};
    public static final String[] ALLOWED_DOCUMENT_TYPES = {"pdf", "doc", "docx", "xls", "xlsx"};
    public static final String UPLOAD_DIR = "uploads/";
    public static final String IMAGE_DIR = UPLOAD_DIR + "images/";
    public static final String DOCUMENT_DIR = UPLOAD_DIR + "documents/";
    
    // ===== BUSINESS LOGIC =====
    public static final int MIN_ORDER_QUANTITY = 1;
    public static final int MAX_ORDER_QUANTITY = 999;
    public static final double MIN_PRODUCT_PRICE = 0.01;
    public static final double MAX_PRODUCT_PRICE = 999999.99;
    public static final int PRODUCT_NAME_MAX_LENGTH = 200;
    public static final int CATEGORY_NAME_MAX_LENGTH = 100;
    
    // ===== STATUS =====
    public static final String STATUS_ACTIVE = "ACTIVE";
    public static final String STATUS_INACTIVE = "INACTIVE";
    public static final String STATUS_PENDING = "PENDING";
    public static final String STATUS_APPROVED = "APPROVED";
    public static final String STATUS_REJECTED = "REJECTED";
    public static final String STATUS_CANCELLED = "CANCELLED";
    public static final String STATUS_COMPLETED = "COMPLETED";
    
    // ===== ORDER STATUS =====
    public static final String ORDER_STATUS_PENDING = "PENDING";
    public static final String ORDER_STATUS_CONFIRMED = "CONFIRMED";
    public static final String ORDER_STATUS_PROCESSING = "PROCESSING";
    public static final String ORDER_STATUS_SHIPPED = "SHIPPED";
    public static final String ORDER_STATUS_DELIVERED = "DELIVERED";
    public static final String ORDER_STATUS_CANCELLED = "CANCELLED";
    public static final String ORDER_STATUS_RETURNED = "RETURNED";
    
    // ===== PAYMENT STATUS =====
    public static final String PAYMENT_STATUS_PENDING = "PENDING";
    public static final String PAYMENT_STATUS_PAID = "PAID";
    public static final String PAYMENT_STATUS_FAILED = "FAILED";
    public static final String PAYMENT_STATUS_REFUNDED = "REFUNDED";
    
    // ===== MESSAGES =====
    public static final String SUCCESS_MESSAGE = "Operation completed successfully";
    public static final String CREATED_MESSAGE = "Resource created successfully";
    public static final String UPDATED_MESSAGE = "Resource updated successfully";
    public static final String DELETED_MESSAGE = "Resource deleted successfully";
    public static final String NOT_FOUND_MESSAGE = "Resource not found";
    public static final String UNAUTHORIZED_MESSAGE = "Unauthorized access";
    public static final String FORBIDDEN_MESSAGE = "Access forbidden";
    public static final String VALIDATION_ERROR_MESSAGE = "Validation failed";
    
    // ===== ENCODING =====
    public static final String UTF8_ENCODING = "UTF-8";
    public static final String ISO_ENCODING = "ISO-8859-1";
    
    // ===== TIMEZONE =====
    public static final String DEFAULT_TIMEZONE = "Asia/Ho_Chi_Minh";
    public static final String UTC_TIMEZONE = "UTC";
}
