package com.gianghp.dried_tea_shop.common.enums;

import lombok.Getter;

/**
 * Enum chứa các mã lỗi và mô tả tương ứng
 */
@Getter
public enum ErrorCode {
    // Success
    SUCCESS("0000", "Success"),
    
    // Common errors (1xxx)
    INTERNAL_SERVER_ERROR("1000", "Internal server error"),
    INVALID_REQUEST("1001", "Invalid request"),
    UNAUTHORIZED("1002", "Unauthorized"),
    FORBIDDEN("1003", "Forbidden"),
    BAD_REQUEST("1004", "Bad request"),
    
    // Validation errors (2xxx)
    VALIDATION_ERROR("2000", "Validation error"),
    REQUIRED_FIELD_MISSING("2001", "Required field is missing"),
    INVALID_FORMAT("2002", "Invalid format"),
    INVALID_EMAIL_FORMAT("2003", "Invalid email format"),
    INVALID_PHONE_FORMAT("2004", "Invalid phone number format"),
    PASSWORD_TOO_WEAK("2005", "Password is too weak"),
    
    // Business errors (3xxx)
    BUSINESS_ERROR("3000", "Business logic error"),
    DUPLICATE_ENTRY("3001", "Duplicate entry"),
    RESOURCE_CONFLICT("3002", "Resource conflict"),
    OPERATION_NOT_ALLOWED("3003", "Operation not allowed"),
    
    // Not found errors (4xxx)
    RESOURCE_NOT_FOUND("4000", "Resource not found"),
    USER_NOT_FOUND("4001", "User not found"),
    PRODUCT_NOT_FOUND("4002", "Product not found"),
    ORDER_NOT_FOUND("4003", "Order not found"),
    CATEGORY_NOT_FOUND("4004", "Category not found"),
    
    // Authentication & Authorization errors (5xxx)
    AUTHENTICATION_FAILED("5000", "Authentication failed"),
    INVALID_CREDENTIALS("5001", "Invalid credentials"),
    TOKEN_EXPIRED("5002", "Token expired"),
    TOKEN_INVALID("5003", "Token invalid"),
    ACCESS_DENIED("5004", "Access denied"),
    
    // Database errors (6xxx)
    DATABASE_ERROR("6000", "Database error"),
    CONNECTION_TIMEOUT("6001", "Database connection timeout"),
    CONSTRAINT_VIOLATION("6002", "Database constraint violation"),
    
    // External service errors (7xxx)
    EXTERNAL_SERVICE_ERROR("7000", "External service error"),
    PAYMENT_SERVICE_ERROR("7001", "Payment service error"),
    EMAIL_SERVICE_ERROR("7002", "Email service error"),
    SMS_SERVICE_ERROR("7003", "SMS service error");
    
    private final String code;
    private final String message;
    
    ErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
