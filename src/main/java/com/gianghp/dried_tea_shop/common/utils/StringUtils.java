package com.gianghp.dried_tea_shop.common.utils;

import com.gianghp.dried_tea_shop.common.constants.AppConstants;

import java.text.Normalizer;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Utility class cho các thao tác với String
 */
public final class StringUtils {
    
    // Private constructor để ngăn việc khởi tạo instance
    private StringUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
    
    // ===== PATTERNS =====
    private static final Pattern EMAIL_PATTERN = Pattern.compile(AppConstants.EMAIL_REGEX);
    private static final Pattern PHONE_PATTERN = Pattern.compile(AppConstants.PHONE_REGEX);
    private static final Pattern USERNAME_PATTERN = Pattern.compile(AppConstants.USERNAME_REGEX);
    private static final Pattern PASSWORD_PATTERN = Pattern.compile(AppConstants.PASSWORD_REGEX);
    
    /**
     * <PERSON>ểm tra string có null hoặc empty không
     */
    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }
    
    /**
     * Kiểm tra string có null, empty hoặc chỉ chứa whitespace không
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * Kiểm tra string có không null và không empty
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }
    
    /**
     * Kiểm tra string có không null, không empty và không chỉ chứa whitespace
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }
    
    /**
     * Trim string, trả về null nếu input null
     */
    public static String trim(String str) {
        return str == null ? null : str.trim();
    }
    
    /**
     * Trim string, trả về empty string nếu input null
     */
    public static String trimToEmpty(String str) {
        return str == null ? "" : str.trim();
    }
    
    /**
     * Trim string, trả về null nếu kết quả là empty string
     */
    public static String trimToNull(String str) {
        String trimmed = trim(str);
        return isEmpty(trimmed) ? null : trimmed;
    }
    
    /**
     * Trả về default value nếu string null hoặc empty
     */
    public static String defaultIfEmpty(String str, String defaultValue) {
        return isEmpty(str) ? defaultValue : str;
    }
    
    /**
     * Trả về default value nếu string null, empty hoặc blank
     */
    public static String defaultIfBlank(String str, String defaultValue) {
        return isBlank(str) ? defaultValue : str;
    }
    
    /**
     * Chuyển đổi string thành lowercase
     */
    public static String toLowerCase(String str) {
        return str == null ? null : str.toLowerCase();
    }
    
    /**
     * Chuyển đổi string thành uppercase
     */
    public static String toUpperCase(String str) {
        return str == null ? null : str.toUpperCase();
    }
    
    /**
     * Capitalize first letter của string
     */
    public static String capitalize(String str) {
        if (isEmpty(str)) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
    
    /**
     * Capitalize first letter của mỗi từ
     */
    public static String capitalizeWords(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        String[] words = str.split("\\s+");
        return Arrays.stream(words)
                .map(StringUtils::capitalize)
                .collect(Collectors.joining(" "));
    }
    
    /**
     * Reverse string
     */
    public static String reverse(String str) {
        if (isEmpty(str)) {
            return str;
        }
        return new StringBuilder(str).reverse().toString();
    }
    
    /**
     * Kiểm tra string có chứa substring không (case-sensitive)
     */
    public static boolean contains(String str, String substring) {
        if (str == null || substring == null) {
            return false;
        }
        return str.contains(substring);
    }
    
    /**
     * Kiểm tra string có chứa substring không (case-insensitive)
     */
    public static boolean containsIgnoreCase(String str, String substring) {
        if (str == null || substring == null) {
            return false;
        }
        return str.toLowerCase().contains(substring.toLowerCase());
    }
    
    /**
     * Kiểm tra string có bắt đầu bằng prefix không
     */
    public static boolean startsWith(String str, String prefix) {
        if (str == null || prefix == null) {
            return false;
        }
        return str.startsWith(prefix);
    }
    
    /**
     * Kiểm tra string có kết thúc bằng suffix không
     */
    public static boolean endsWith(String str, String suffix) {
        if (str == null || suffix == null) {
            return false;
        }
        return str.endsWith(suffix);
    }
    
    /**
     * Cắt string nếu vượt quá độ dài cho phép
     */
    public static String truncate(String str, int maxLength) {
        if (isEmpty(str) || maxLength < 0) {
            return str;
        }
        if (str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength);
    }
    
    /**
     * Cắt string và thêm suffix nếu vượt quá độ dài
     */
    public static String truncate(String str, int maxLength, String suffix) {
        if (isEmpty(str) || maxLength < 0) {
            return str;
        }
        if (str.length() <= maxLength) {
            return str;
        }
        
        String safeSuffix = defaultIfEmpty(suffix, "...");
        int truncateLength = maxLength - safeSuffix.length();
        if (truncateLength < 0) {
            return safeSuffix.substring(0, maxLength);
        }
        
        return str.substring(0, truncateLength) + safeSuffix;
    }
    
    /**
     * Pad string bên trái với character
     */
    public static String leftPad(String str, int size, char padChar) {
        if (str == null) {
            return null;
        }
        if (str.length() >= size) {
            return str;
        }
        
        StringBuilder sb = new StringBuilder(size);
        for (int i = 0; i < size - str.length(); i++) {
            sb.append(padChar);
        }
        sb.append(str);
        return sb.toString();
    }
    
    /**
     * Pad string bên phải với character
     */
    public static String rightPad(String str, int size, char padChar) {
        if (str == null) {
            return null;
        }
        if (str.length() >= size) {
            return str;
        }
        
        StringBuilder sb = new StringBuilder(str);
        for (int i = 0; i < size - str.length(); i++) {
            sb.append(padChar);
        }
        return sb.toString();
    }
    
    /**
     * Join array of strings với delimiter
     */
    public static String join(String[] array, String delimiter) {
        if (array == null || array.length == 0) {
            return "";
        }
        return String.join(defaultIfEmpty(delimiter, ""), array);
    }
    
    /**
     * Join collection of strings với delimiter
     */
    public static String join(Collection<String> collection, String delimiter) {
        if (collection == null || collection.isEmpty()) {
            return "";
        }
        return String.join(defaultIfEmpty(delimiter, ""), collection);
    }
    
    /**
     * Split string thành array, loại bỏ empty strings
     */
    public static String[] split(String str, String delimiter) {
        if (isEmpty(str)) {
            return new String[0];
        }
        return str.split(Pattern.quote(defaultIfEmpty(delimiter, ",")));
    }
    
    /**
     * Đếm số lần xuất hiện của substring trong string
     */
    public static int countOccurrences(String str, String substring) {
        if (isEmpty(str) || isEmpty(substring)) {
            return 0;
        }
        
        int count = 0;
        int index = 0;
        while ((index = str.indexOf(substring, index)) != -1) {
            count++;
            index += substring.length();
        }
        return count;
    }
    
    /**
     * Loại bỏ dấu tiếng Việt
     */
    public static String removeAccents(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        String normalized = Normalizer.normalize(str, Normalizer.Form.NFD);
        return normalized.replaceAll("\\p{M}", "");
    }
    
    /**
     * Chuyển đổi string thành slug (URL-friendly)
     */
    public static String toSlug(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        String slug = removeAccents(str.toLowerCase());
        slug = slug.replaceAll("[^a-z0-9\\s-]", "");
        slug = slug.replaceAll("\\s+", "-");
        slug = slug.replaceAll("-+", "-");
        slug = slug.replaceAll("^-|-$", "");
        
        return slug;
    }
    
    /**
     * Validate email format
     */
    public static boolean isValidEmail(String email) {
        if (isEmpty(email)) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email).matches();
    }
    
    /**
     * Validate phone number format
     */
    public static boolean isValidPhone(String phone) {
        if (isEmpty(phone)) {
            return false;
        }
        return PHONE_PATTERN.matcher(phone).matches();
    }
    
    /**
     * Validate username format
     */
    public static boolean isValidUsername(String username) {
        if (isEmpty(username)) {
            return false;
        }
        return USERNAME_PATTERN.matcher(username).matches();
    }
    
    /**
     * Validate password strength
     */
    public static boolean isValidPassword(String password) {
        if (isEmpty(password)) {
            return false;
        }
        return PASSWORD_PATTERN.matcher(password).matches();
    }
    
    /**
     * Mask string (ẩn một phần string)
     */
    public static String mask(String str, int visibleStart, int visibleEnd, char maskChar) {
        if (isEmpty(str)) {
            return str;
        }
        
        int length = str.length();
        if (visibleStart + visibleEnd >= length) {
            return str;
        }
        
        StringBuilder masked = new StringBuilder();
        masked.append(str.substring(0, visibleStart));
        
        for (int i = visibleStart; i < length - visibleEnd; i++) {
            masked.append(maskChar);
        }
        
        masked.append(str.substring(length - visibleEnd));
        return masked.toString();
    }
    
    /**
     * Mask email (ẩn một phần email)
     */
    public static String maskEmail(String email) {
        if (!isValidEmail(email)) {
            return email;
        }
        
        int atIndex = email.indexOf('@');
        String username = email.substring(0, atIndex);
        String domain = email.substring(atIndex);
        
        if (username.length() <= 2) {
            return mask(username, 1, 0, '*') + domain;
        } else {
            return mask(username, 2, 1, '*') + domain;
        }
    }
    
    /**
     * Mask phone number
     */
    public static String maskPhone(String phone) {
        if (isEmpty(phone)) {
            return phone;
        }
        
        if (phone.length() <= 4) {
            return phone;
        }
        
        return mask(phone, 2, 2, '*');
    }
}
