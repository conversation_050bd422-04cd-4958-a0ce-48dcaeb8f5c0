package com.gianghp.dried_tea_shop.common.utils;

import com.gianghp.dried_tea_shop.common.constants.AppConstants;
import lombok.extern.slf4j.Slf4j;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * Utility class cho các thao tác với Date và Time
 */
@Slf4j
public final class DateTimeUtils {
    
    // Private constructor để ngăn việc khởi tạo instance
    private DateTimeUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
    
    // ===== FORMATTERS =====
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(AppConstants.DATE_FORMAT);
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(AppConstants.DATE_TIME_FORMAT);
    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(AppConstants.TIME_FORMAT);
    public static final DateTimeFormatter ISO_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(AppConstants.ISO_DATE_TIME_FORMAT);
    
    // ===== TIMEZONE =====
    public static final ZoneId DEFAULT_ZONE = ZoneId.of(AppConstants.DEFAULT_TIMEZONE);
    public static final ZoneId UTC_ZONE = ZoneId.of(AppConstants.UTC_TIMEZONE);
    
    /**
     * Lấy thời gian hiện tại
     */
    public static LocalDateTime now() {
        return LocalDateTime.now(DEFAULT_ZONE);
    }
    
    /**
     * Lấy thời gian hiện tại theo UTC
     */
    public static LocalDateTime nowUtc() {
        return LocalDateTime.now(UTC_ZONE);
    }
    
    /**
     * Lấy ngày hiện tại
     */
    public static LocalDate today() {
        return LocalDate.now(DEFAULT_ZONE);
    }
    
    /**
     * Lấy thời gian hiện tại
     */
    public static LocalTime currentTime() {
        return LocalTime.now(DEFAULT_ZONE);
    }
    
    /**
     * Format LocalDateTime thành string
     */
    public static String format(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATE_TIME_FORMATTER);
    }
    
    /**
     * Format LocalDateTime thành string với pattern tùy chỉnh
     */
    public static String format(LocalDateTime dateTime, String pattern) {
        if (dateTime == null || StringUtils.isBlank(pattern)) {
            return null;
        }
        try {
            return dateTime.format(DateTimeFormatter.ofPattern(pattern));
        } catch (Exception e) {
            log.error("Error formatting datetime with pattern {}: {}", pattern, e.getMessage());
            return null;
        }
    }
    
    /**
     * Format LocalDate thành string
     */
    public static String format(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.format(DATE_FORMATTER);
    }
    
    /**
     * Format LocalTime thành string
     */
    public static String format(LocalTime time) {
        if (time == null) {
            return null;
        }
        return time.format(TIME_FORMATTER);
    }
    
    /**
     * Parse string thành LocalDateTime
     */
    public static LocalDateTime parseDateTime(String dateTimeString) {
        if (StringUtils.isBlank(dateTimeString)) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeString, DATE_TIME_FORMATTER);
        } catch (DateTimeParseException e) {
            log.error("Error parsing datetime string {}: {}", dateTimeString, e.getMessage());
            return null;
        }
    }
    
    /**
     * Parse string thành LocalDateTime với pattern tùy chỉnh
     */
    public static LocalDateTime parseDateTime(String dateTimeString, String pattern) {
        if (StringUtils.isBlank(dateTimeString) || StringUtils.isBlank(pattern)) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeString, DateTimeFormatter.ofPattern(pattern));
        } catch (DateTimeParseException e) {
            log.error("Error parsing datetime string {} with pattern {}: {}", dateTimeString, pattern, e.getMessage());
            return null;
        }
    }
    
    /**
     * Parse string thành LocalDate
     */
    public static LocalDate parseDate(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return null;
        }
        try {
            return LocalDate.parse(dateString, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            log.error("Error parsing date string {}: {}", dateString, e.getMessage());
            return null;
        }
    }
    
    /**
     * Parse string thành LocalTime
     */
    public static LocalTime parseTime(String timeString) {
        if (StringUtils.isBlank(timeString)) {
            return null;
        }
        try {
            return LocalTime.parse(timeString, TIME_FORMATTER);
        } catch (DateTimeParseException e) {
            log.error("Error parsing time string {}: {}", timeString, e.getMessage());
            return null;
        }
    }
    
    /**
     * Chuyển đổi LocalDateTime sang Date
     */
    public static Date toDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(DEFAULT_ZONE).toInstant());
    }
    
    /**
     * Chuyển đổi Date sang LocalDateTime
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(DEFAULT_ZONE).toLocalDateTime();
    }
    
    /**
     * Chuyển đổi LocalDateTime sang timestamp (milliseconds)
     */
    public static long toTimestamp(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return 0L;
        }
        return localDateTime.atZone(DEFAULT_ZONE).toInstant().toEpochMilli();
    }
    
    /**
     * Chuyển đổi timestamp sang LocalDateTime
     */
    public static LocalDateTime fromTimestamp(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), DEFAULT_ZONE);
    }
    
    /**
     * Tính khoảng cách giữa hai thời điểm (theo ngày)
     */
    public static long daysBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0L;
        }
        return ChronoUnit.DAYS.between(start, end);
    }
    
    /**
     * Tính khoảng cách giữa hai thời điểm (theo giờ)
     */
    public static long hoursBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0L;
        }
        return ChronoUnit.HOURS.between(start, end);
    }
    
    /**
     * Tính khoảng cách giữa hai thời điểm (theo phút)
     */
    public static long minutesBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0L;
        }
        return ChronoUnit.MINUTES.between(start, end);
    }
    
    /**
     * Kiểm tra xem một thời điểm có nằm trong khoảng thời gian không
     */
    public static boolean isBetween(LocalDateTime dateTime, LocalDateTime start, LocalDateTime end) {
        if (dateTime == null || start == null || end == null) {
            return false;
        }
        return !dateTime.isBefore(start) && !dateTime.isAfter(end);
    }
    
    /**
     * Lấy đầu ngày (00:00:00)
     */
    public static LocalDateTime startOfDay(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.atStartOfDay();
    }
    
    /**
     * Lấy cuối ngày (23:59:59.999999999)
     */
    public static LocalDateTime endOfDay(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.atTime(LocalTime.MAX);
    }
    
    /**
     * Lấy đầu tháng
     */
    public static LocalDate startOfMonth(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.withDayOfMonth(1);
    }
    
    /**
     * Lấy cuối tháng
     */
    public static LocalDate endOfMonth(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.withDayOfMonth(date.lengthOfMonth());
    }
    
    /**
     * Kiểm tra xem có phải cuối tuần không (Thứ 7 hoặc Chủ nhật)
     */
    public static boolean isWeekend(LocalDate date) {
        if (date == null) {
            return false;
        }
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;
    }
    
    /**
     * Thêm số ngày vào thời điểm
     */
    public static LocalDateTime plusDays(LocalDateTime dateTime, long days) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.plusDays(days);
    }
    
    /**
     * Thêm số giờ vào thời điểm
     */
    public static LocalDateTime plusHours(LocalDateTime dateTime, long hours) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.plusHours(hours);
    }
    
    /**
     * Thêm số phút vào thời điểm
     */
    public static LocalDateTime plusMinutes(LocalDateTime dateTime, long minutes) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.plusMinutes(minutes);
    }
}
