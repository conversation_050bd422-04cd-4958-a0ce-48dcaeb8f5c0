package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.OrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface OrderItemRepository extends JpaRepository<OrderItem, UUID> {
    
    /**
     * Find order items by order ID
     */
    List<OrderItem> findByOrderId(UUID orderId);
    
    /**
     * Find order items by product ID
     */
    List<OrderItem> findByProductId(UUID productId);
    
    /**
     * Find order items by order and product
     */
    List<OrderItem> findByOrderIdAndProductId(UUID orderId, UUID productId);
    
    /**
     * Find order items by quantity
     */
    List<OrderItem> findByQuantity(Integer quantity);
    
    /**
     * Find order items by quantity range
     */
    List<OrderItem> findByQuantityBetween(Integer minQuantity, Integer maxQuantity);

    /**
     * Find order items by unit price range
     */
    List<OrderItem> findByUnitPriceBetween(BigDecimal minPrice, BigDecimal maxPrice);

    /**
     * Count order items by order ID
     */
    long countByOrderId(UUID orderId);

    /**
     * Count order items by product ID
     */
    long countByProductId(UUID productId);

    /**
     * Find order items with high quantity
     */
    List<OrderItem> findByQuantityGreaterThan(Integer quantity);

    // Removed complex queries - will be handled by service layer if needed
}
