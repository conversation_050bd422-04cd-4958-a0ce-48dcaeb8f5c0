package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.CartItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CartItemRepository extends JpaRepository<CartItem, UUID> {

    /**
     * Find cart items by user ID
     */
    List<CartItem> findByUserId(UUID userId);

    /**
     * Find cart items by product ID
     */
    List<CartItem> findByProductId(UUID productId);

    /**
     * Find cart item by user and product
     */
    Optional<CartItem> findByUserIdAndProductId(UUID userId, UUID productId);

    /**
     * Check if cart item exists for user and product
     */
    boolean existsByUserIdAndProductId(UUID userId, UUID productId);

    /**
     * Delete cart items by user ID
     */
    void deleteByUserId(UUID userId);

    /**
     * Delete cart items by product ID
     */
    void deleteByProductId(UUID productId);

    /**
     * Count cart items by user ID
     */
    long countByUserId(UUID userId);

    // Removed complex queries - will be handled by service layer if needed
}
