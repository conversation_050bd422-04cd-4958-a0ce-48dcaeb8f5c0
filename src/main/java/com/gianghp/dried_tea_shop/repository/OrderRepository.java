package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Order;
import com.gianghp.dried_tea_shop.enums.OrderStatus;
import com.gianghp.dried_tea_shop.enums.PaymentMethod;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface OrderRepository extends JpaRepository<Order, UUID> {
    
    /**
     * Find orders by user ID
     */
    List<Order> findByUserId(UUID userId);
    
    /**
     * Find orders by user ID with pagination
     */
    Page<Order> findByUserId(UUID userId, Pageable pageable);
    
    /**
     * Find orders by status
     */
    List<Order> findByStatus(OrderStatus status);
    
    /**
     * Find orders by status with pagination
     */
    Page<Order> findByStatus(OrderStatus status, Pageable pageable);
    
    /**
     * Find orders by user and status
     */
    List<Order> findByUserIdAndStatus(UUID userId, OrderStatus status);
    
    /**
     * Find orders by payment method
     */
    List<Order> findByPaymentMethod(PaymentMethod paymentMethod);
    
    /**
     * Find orders by total amount range
     */
    List<Order> findByTotalAmountBetween(BigDecimal minAmount, BigDecimal maxAmount);

    /**
     * Count orders by status
     */
    long countByStatus(OrderStatus status);

    /**
     * Count orders by user
     */
    long countByUserId(UUID userId);

    /**
     * Find orders by status and payment method
     */
    List<Order> findByStatusAndPaymentMethod(OrderStatus status, PaymentMethod paymentMethod);

    // Removed complex queries - will be handled by service layer if needed
}
