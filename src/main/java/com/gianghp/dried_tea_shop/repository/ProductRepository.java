package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.dto.product.ProductSearchResultDto;
import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.enums.ProductStatus;
import java.util.Arrays;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Repository
public interface ProductRepository extends JpaRepository<Product, UUID>,
    JpaSpecificationExecutor<Product> {

  @Query(value = """
      WITH search_query AS (
          SELECT unaccent(:keyword) AS q
      )
      SELECT p.id,
             p.name,
             p.description,
             p.price,
             p.review_count,
             p.rating,
             c.name AS category_name,
             similarity(unaccent(p.name), sq.q) AS sim_name,
             ts_rank(to_tsvector('simple', unaccent(p.name)), plainto_tsquery('simple', sq.q)) AS rank_name,
             ts_rank(to_tsvector('simple', unaccent(p.description)), plainto_tsquery('simple', sq.q)) AS rank_desc,
             ts_rank(to_tsvector('simple', unaccent(c.name)), plainto_tsquery('simple', sq.q)) AS rank_cat,
             ts_rank(to_tsvector('simple', unaccent(c.description)), plainto_tsquery('simple', sq.q)) AS rank_cat_desc
      FROM product p
      JOIN category c ON p.category_id = c.id
      CROSS JOIN search_query sq
      WHERE (
              unaccent(p.name) % sq.q
           OR to_tsvector('simple', unaccent(p.name)) @@ plainto_tsquery('simple', sq.q)
           OR to_tsvector('simple', unaccent(p.description)) @@ plainto_tsquery('simple', sq.q)
           OR to_tsvector('simple', unaccent(c.name)) @@ plainto_tsquery('simple', sq.q)
           OR to_tsvector('simple', unaccent(c.description)) @@ plainto_tsquery('simple', sq.q)
      )
      AND (:status IS NULL OR p.status = :status)
      AND (:minPrice IS NULL OR p.price >= :minPrice)
      AND (:maxPrice IS NULL OR p.price <= :maxPrice)
      AND (:minReview IS NULL OR p.review_count >= :minReview)
      AND (:categoryId IS NULL OR p.category_id = :categoryId)
      ORDER BY
          rank_name DESC NULLS LAST,
          sim_name DESC NULLS LAST,
          rank_desc DESC NULLS LAST,
          rank_cat DESC NULLS LAST,
          rank_cat_desc DESC NULLS LAST
      LIMIT :limit
      """, nativeQuery = true)
  List<Object[]> searchProducts(
      @Param("keyword") String keyword,
      @Param("status") String status,
      @Param("minPrice") BigDecimal minPrice,
      @Param("maxPrice") BigDecimal maxPrice,
      @Param("minReview") Integer minReview,
      @Param("categoryId") UUID categoryId,
      @Param("limit") int limit
  );

  // Removed complex queries - will be handled by service layer if needed
}
