package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Discount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface DiscountRepository extends JpaRepository<Discount, UUID> {
    
    /**
     * Find discount by name
     */
    Optional<Discount> findByName(String name);
    
    /**
     * Check if name exists
     */
    boolean existsByName(String name);
    
    /**
     * Find discounts by active status
     */
    List<Discount> findByActive(Boolean active);
    
    /**
     * Find discounts by discount type
     */
    List<Discount> findByDiscountType(String discountType);
    
    /**
     * Find active discounts by type
     */
    List<Discount> findByActiveAndDiscountType(Boolean active, String discountType);
    
    /**
     * Find discounts by name containing (case insensitive)
     */
    List<Discount> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find discounts by value range
     */
    List<Discount> findByValueBetween(BigDecimal minValue, BigDecimal maxValue);

    // Removed complex queries - will be handled by service layer if needed
}
