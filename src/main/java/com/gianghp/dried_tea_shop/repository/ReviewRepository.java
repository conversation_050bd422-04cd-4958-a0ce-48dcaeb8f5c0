package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Review;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ReviewRepository extends JpaRepository<Review, UUID> {
    
    /**
     * Find reviews by product ID
     */
    List<Review> findByProductId(UUID productId);
    
    /**
     * Find reviews by product ID with pagination
     */
    Page<Review> findByProductId(UUID productId, Pageable pageable);
    
    /**
     * Find reviews by user ID
     */
    List<Review> findByUserId(UUID userId);
    
    /**
     * Find reviews by user ID with pagination
     */
    Page<Review> findByUserId(UUID userId, Pageable pageable);
    
    /**
     * Find review by user and product
     */
    Optional<Review> findByUserIdAndProductId(UUID userId, UUID productId);
    
    /**
     * Check if review exists for user and product
     */
    boolean existsByUserIdAndProductId(UUID userId, UUID productId);

    /**
     * Find reviews by rating
     */
    List<Review> findByRating(Integer rating);

    /**
     * Find reviews by rating range
     */
    List<Review> findByRatingBetween(Integer minRating, Integer maxRating);

    /**
     * Find reviews by product and rating
     */
    List<Review> findByProductIdAndRating(UUID productId, Integer rating);

    /**
     * Count reviews by product
     */
    long countByProductId(UUID productId);

    /**
     * Count reviews by user
     */
    long countByUserId(UUID userId);

    /**
     * Count reviews by rating
     */
    long countByRating(Integer rating);

    // Removed complex queries - will be handled by service layer if needed
}
