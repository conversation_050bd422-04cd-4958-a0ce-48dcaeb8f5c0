package com.gianghp.dried_tea_shop.mapper;

import com.gianghp.dried_tea_shop.dto.common.ImageDto;
import com.gianghp.dried_tea_shop.entity.Image;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {UuidMapper.class})
public interface ImageMapper {

    @Mapping(source = "id", target = "imageId", qualifiedByName = "uuidToString")
    ImageDto toDto(Image image);

    @Mapping(source = "imageId", target = "id", qualifiedByName = "stringToUuid")
    Image toEntity(ImageDto imageDto);
}
