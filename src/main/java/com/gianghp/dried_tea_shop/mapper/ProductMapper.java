package com.gianghp.dried_tea_shop.mapper;

import com.gianghp.dried_tea_shop.dto.common.ImageDto;
import com.gianghp.dried_tea_shop.dto.product.ProductRequestDto;
import com.gianghp.dried_tea_shop.dto.product.ProductResponseDto;
import com.gianghp.dried_tea_shop.dto.product.ProductSearchResultDto;
import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.enums.ProductStatus;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring", uses = {ImageMapper.class, UuidMapper.class})
public interface ProductMapper {

    @Mapping(source = "id", target = "productId", qualifiedByName = "uuidToString")
    @Mapping(source = "category.name", target = "categoryName")
    @Mapping(source = "discount.value", target = "discountValue")
    @Mapping(source = "image", target = "image")
    @Mapping(source = "createdAt", target = "createdAt", dateFormat = "yyyy-MM-dd'T'HH:mm:ss")
    ProductResponseDto toResponseDto(Product product);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "rating", ignore = true)
    @Mapping(target = "reviewCount", ignore = true)
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "discount", ignore = true)
    @Mapping(target = "image", ignore = true)
    @Mapping(target = "nameEn", ignore = true)
    @Mapping(target = "descriptionEn", ignore = true)
    Product toEntity(ProductRequestDto productRequestDto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "rating", ignore = true)
    @Mapping(target = "reviewCount", ignore = true)
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "discount", ignore = true)
    @Mapping(target = "image", ignore = true)
    @Mapping(target = "nameEn", ignore = true)
    @Mapping(target = "descriptionEn", ignore = true)
    void updateEntityFromDto(ProductRequestDto productRequestDto, @MappingTarget Product product);


    default ProductResponseDto toProductResponseDto(Object[] row) {
        if (row == null) return null;

        ProductResponseDto dto = new ProductResponseDto();
        dto.setProductId(row[0] != null ? row[0].toString() : null);  // UUID → String
        dto.setName((String) row[1]);
        dto.setDescription((String) row[2]);
        dto.setPrice(row[3] != null ? (BigDecimal) row[3] : BigDecimal.ZERO);
        dto.setReviewCount(row[4] != null ? ((Number) row[4]).intValue() : 0);
        dto.setRating(row[5] != null ? (BigDecimal) row[5] : BigDecimal.ZERO);
        dto.setCategoryName((String) row[6]);

        // Các trường còn lại nếu query không trả về thì set default/null
        dto.setStockQuantity(0);
        dto.setDiscountValue(null);
        dto.setImage(null);
        dto.setStatus(null);
        dto.setCreatedAt(null);

        return dto;
    }

    default List<ProductResponseDto> toProductResponseDtoList(List<Object[]> rows) {
        if (rows == null) return List.of();
        return rows.stream()
            .map(this::toProductResponseDto)
            .toList();
    }

}
