package com.gianghp.dried_tea_shop.mapper;

import com.gianghp.dried_tea_shop.dto.order.CreateOrderRequestDto;
import com.gianghp.dried_tea_shop.dto.order.OrderItemRequestDto;
import com.gianghp.dried_tea_shop.dto.order.OrderItemResponseDto;
import com.gianghp.dried_tea_shop.dto.order.OrderResponseDto;
import com.gianghp.dried_tea_shop.dto.order.UpdateOrderStatusRequestDto;
import com.gianghp.dried_tea_shop.entity.Order;
import com.gianghp.dried_tea_shop.entity.OrderItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring", uses = {UuidMapper.class})
public interface OrderMapper {

    @Mapping(source = "id", target = "orderId", qualifiedByName = "uuidToString")
    @Mapping(source = "user.id", target = "userId", qualifiedByName = "uuidToString")
    @Mapping(source = "createdAt", target = "createdAt", dateFormat = "yyyy-MM-dd'T'HH:mm:ss")
    @Mapping(target = "items", ignore = true) // Will be populated by service layer
    OrderResponseDto toResponseDto(Order order);

    @Mapping(source = "id", target = "orderItemId", qualifiedByName = "uuidToString")
    @Mapping(source = "order.id", target = "orderId", qualifiedByName = "uuidToString")
    @Mapping(source = "product.id", target = "productId", qualifiedByName = "uuidToString")
    OrderItemResponseDto toOrderItemResponseDto(OrderItem orderItem);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "totalAmount", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "user", ignore = true)
    Order toEntity(CreateOrderRequestDto createOrderRequestDto);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "order", ignore = true)
    @Mapping(target = "product", ignore = true)
    @Mapping(target = "unitPrice", ignore = true)
    OrderItem toOrderItemEntity(OrderItemRequestDto orderItemRequestDto);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "totalAmount", ignore = true)
    @Mapping(target = "paymentMethod", ignore = true)
    @Mapping(target = "user", ignore = true)
    void updateEntityFromDto(UpdateOrderStatusRequestDto updateOrderStatusRequestDto, @MappingTarget Order order);
}
