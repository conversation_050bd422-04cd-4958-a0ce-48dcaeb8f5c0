package com.gianghp.dried_tea_shop.mapper;

import com.gianghp.dried_tea_shop.dto.review.ReviewRequestDto;
import com.gianghp.dried_tea_shop.dto.review.ReviewResponseDto;
import com.gianghp.dried_tea_shop.entity.Review;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring", uses = {UuidMapper.class})
public interface ReviewMapper {

    @Mapping(source = "id", target = "reviewId", qualifiedByName = "uuidToString")
    @Mapping(source = "user.id", target = "userId", qualifiedByName = "uuidToString")
    @Mapping(source = "product.id", target = "productId", qualifiedByName = "uuidToString")
    @Mapping(source = "createdAt", target = "createdAt", dateFormat = "yyyy-MM-dd'T'HH:mm:ss")
    ReviewResponseDto toResponseDto(Review review);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "user", ignore = true)
    @Mapping(target = "product", ignore = true)
    Review toEntity(ReviewRequestDto reviewRequestDto);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "user", ignore = true)
    @Mapping(target = "product", ignore = true)
    void updateEntityFromDto(ReviewRequestDto reviewRequestDto, @MappingTarget Review review);
}
