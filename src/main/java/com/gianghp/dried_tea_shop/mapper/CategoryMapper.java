package com.gianghp.dried_tea_shop.mapper;

import com.gianghp.dried_tea_shop.dto.category.CategoryRequestDto;
import com.gianghp.dried_tea_shop.dto.category.CategoryResponseDto;
import com.gianghp.dried_tea_shop.entity.Category;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring", uses = {UuidMapper.class})
public interface CategoryMapper {

    @Mapping(source = "id", target = "categoryId", qualifiedByName = "uuidToString")
    CategoryResponseDto toResponseDto(Category category);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "nameEn", ignore = true)
    @Mapping(target = "descriptionEn", ignore = true)
    Category toEntity(CategoryRequestDto categoryRequestDto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "nameEn", ignore = true)
    @Mapping(target = "descriptionEn", ignore = true)
    void updateEntityFromDto(CategoryRequestDto categoryRequestDto, @MappingTarget Category category);
}
