package com.gianghp.dried_tea_shop.mapper;

import com.gianghp.dried_tea_shop.dto.discount.DiscountRequestDto;
import com.gianghp.dried_tea_shop.dto.discount.DiscountResponseDto;
import com.gianghp.dried_tea_shop.entity.Discount;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring", uses = {UuidMapper.class})
public interface DiscountMapper {

    @Mapping(source = "id", target = "discountId", qualifiedByName = "uuidToString")
    DiscountResponseDto toResponseDto(Discount discount);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Discount toEntity(DiscountRequestDto discountRequestDto);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(DiscountRequestDto discountRequestDto, @MappingTarget Discount discount);
}
