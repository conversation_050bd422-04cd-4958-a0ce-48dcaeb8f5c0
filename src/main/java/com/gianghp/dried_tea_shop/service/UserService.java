package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.dto.common.UserDto;
import com.gianghp.dried_tea_shop.dto.user.UpdateUserRequestDto;
import com.gianghp.dried_tea_shop.entity.User;
import com.gianghp.dried_tea_shop.mapper.UserMapper;
import com.gianghp.dried_tea_shop.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;

    public List<UserDto> getAllUsers() {
        return userRepository.findAll().stream()
            .map(userMapper::toUserDto)
            .toList();
    }

    public UserDto getUserById(UUID id) {
        return userRepository.findById(id)
            .map(userMapper::toUserDto)
            .orElseThrow(() -> new RuntimeException("User not found"));
    }

    public UserDto updateUser(UUID id, UpdateUserRequestDto updateUserRequestDto) {
        User user = userRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("User not found"));

        userMapper.updateEntityFromDto(updateUserRequestDto, user);
        User savedUser = userRepository.save(user);

        return userMapper.toUserDto(savedUser);
    }
}
