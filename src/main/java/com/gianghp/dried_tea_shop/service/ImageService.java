package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.entity.Image;
import com.gianghp.dried_tea_shop.repository.ImageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ImageService {
    
    private final ImageRepository imageRepository;
    
    // TODO: Implement image service methods
    // - Upload image
    // - Delete image
    // - Find image by ID
    // - Find image by name
    // - Find image by URL
    // - Find all images
    // - Find images with products
    // - Find images without products
    // - Validate image format
    // - Resize image
    // - Generate thumbnail
    // - Clean up unused images
}
