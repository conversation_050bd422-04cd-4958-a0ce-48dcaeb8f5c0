package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.dto.discount.DiscountRequestDto;
import com.gianghp.dried_tea_shop.dto.discount.DiscountResponseDto;
import com.gianghp.dried_tea_shop.entity.Discount;
import com.gianghp.dried_tea_shop.mapper.DiscountMapper;
import com.gianghp.dried_tea_shop.repository.DiscountRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class DiscountService {

    private final DiscountRepository discountRepository;
    private final DiscountMapper discountMapper;

    public List<DiscountResponseDto> getAllDiscounts() {
        return discountRepository.findAll().stream()
            .map(discountMapper::toResponseDto)
            .toList();
    }

    public DiscountResponseDto createDiscount(DiscountRequestDto discountRequestDto) {
        Discount discount = discountMapper.toEntity(discountRequestDto);
        Discount savedDiscount = discountRepository.save(discount);
        return discountMapper.toResponseDto(savedDiscount);
    }

    public DiscountResponseDto updateDiscount(UUID id, DiscountRequestDto discountRequestDto) {
        Discount discount = discountRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Discount not found"));

        discountMapper.updateEntityFromDto(discountRequestDto, discount);
        Discount savedDiscount = discountRepository.save(discount);

        return discountMapper.toResponseDto(savedDiscount);
    }
}
