package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.entity.CartItem;
import com.gianghp.dried_tea_shop.entity.Order;
import com.gianghp.dried_tea_shop.entity.OrderItem;
import com.gianghp.dried_tea_shop.repository.CartItemRepository;
import com.gianghp.dried_tea_shop.repository.OrderItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class OrderItemService {

    private final OrderItemRepository orderItemRepository;
    private final CartItemRepository cartItemRepository;

    /**
     * Create order items from user's cart items
     */
    public List<OrderItem> createOrderItemsFromCart(UUID userId, Order order) {
        List<CartItem> cartItems = cartItemRepository.findByUserId(userId);

        List<OrderItem> orderItems = cartItems.stream()
            .map(cartItem -> {
                OrderItem orderItem = new OrderItem();
                orderItem.setOrder(order);
                orderItem.setProduct(cartItem.getProduct());
                orderItem.setQuantity(cartItem.getQuantity());
                orderItem.setUnitPrice(cartItem.getProduct().getPrice());
                return orderItem;
            })
            .toList();

        return orderItemRepository.saveAll(orderItems);
    }

    /**
     * Clear cart after creating order
     */
    public void clearCartAfterOrder(UUID userId) {
        cartItemRepository.deleteByUserId(userId);
    }

    // TODO: Implement additional order item service methods
    // - Find order items by order ID
    // - Find order items by product ID
    // - Find order items by user ID
    // - Calculate order item total
    // - Update order item quantity
    // - Validate order item
    // - Get best selling products
    // - Get top revenue products
    // - Calculate product sales statistics
    // - Generate sales report
}
