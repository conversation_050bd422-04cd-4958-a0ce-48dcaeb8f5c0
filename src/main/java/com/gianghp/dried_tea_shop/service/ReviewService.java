package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.dto.review.ReviewRequestDto;
import com.gianghp.dried_tea_shop.dto.review.ReviewResponseDto;
import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.entity.Review;
import com.gianghp.dried_tea_shop.entity.User;
import com.gianghp.dried_tea_shop.exception.BusinessException;
import com.gianghp.dried_tea_shop.exception.NotFoundException;
import com.gianghp.dried_tea_shop.mapper.ReviewMapper;
import com.gianghp.dried_tea_shop.repository.ProductRepository;
import com.gianghp.dried_tea_shop.repository.ReviewRepository;
import com.gianghp.dried_tea_shop.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ReviewService {

    private final ReviewRepository reviewRepository;
    private final ReviewMapper reviewMapper;
    private final ProductRepository productRepository;
    private final UserRepository userRepository;

    public Page<ReviewResponseDto> getReviewsByProductId(UUID productId, Pageable pageable) {
        return reviewRepository.findByProductId(productId, pageable)
            .map(reviewMapper::toResponseDto);
    }

    public ReviewResponseDto createReview(UUID productId, UUID userId, ReviewRequestDto reviewRequestDto) {
        // Check if user already reviewed this product
        if (reviewRepository.existsByUserIdAndProductId(userId, productId)) {
            throw new BusinessException(ErrorCode.DUPLICATE_ENTRY, "User has already reviewed this product");
        }

        Product product = productRepository.findById(productId)
            .orElseThrow(() -> new NotFoundException(ErrorCode.PRODUCT_NOT_FOUND));
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new NotFoundException(ErrorCode.USER_NOT_FOUND));

        Review review = reviewMapper.toEntity(reviewRequestDto);
        review.setProduct(product);
        review.setUser(user);

        Review savedReview = reviewRepository.save(review);
        return reviewMapper.toResponseDto(savedReview);
    }

    public ReviewResponseDto updateReview(UUID reviewId, ReviewRequestDto reviewRequestDto) {
        Review review = reviewRepository.findById(reviewId)
            .orElseThrow(() -> new NotFoundException("Review", reviewId));

        reviewMapper.updateEntityFromDto(reviewRequestDto, review);
        Review savedReview = reviewRepository.save(review);

        return reviewMapper.toResponseDto(savedReview);
    }
}
