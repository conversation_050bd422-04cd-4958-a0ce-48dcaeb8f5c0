package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.dto.category.CategoryRequestDto;
import com.gianghp.dried_tea_shop.dto.category.CategoryResponseDto;
import com.gianghp.dried_tea_shop.entity.Category;
import com.gianghp.dried_tea_shop.mapper.CategoryMapper;
import com.gianghp.dried_tea_shop.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CategoryService {

  private final CategoryRepository categoryRepository;
  private final CategoryMapper categoryMapper;

  public List<CategoryResponseDto> getAllCategories() {
    return categoryRepository.findAll().stream()
        .map(categoryMapper::toResponseDto)
        .toList();
  }

  public CategoryResponseDto createCategory(CategoryRequestDto categoryRequestDto) {
    Category category = categoryMapper.toEntity(categoryRequestDto);
    Category savedCategory = categoryRepository.save(category);
    return categoryMapper.toResponseDto(savedCategory);
  }

  public CategoryResponseDto updateCategory(UUID id, CategoryRequestDto categoryRequestDto) {
    Category category = categoryRepository.findById(id)
        .orElseThrow(() -> new RuntimeException("Category not found"));

    categoryMapper.updateEntityFromDto(categoryRequestDto, category);
    Category savedCategory = categoryRepository.save(category);

    return categoryMapper.toResponseDto(savedCategory);
  }
}
