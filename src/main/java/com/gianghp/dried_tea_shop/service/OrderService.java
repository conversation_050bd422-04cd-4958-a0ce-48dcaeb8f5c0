package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.dto.order.CreateOrderRequestDto;
import com.gianghp.dried_tea_shop.dto.order.OrderItemResponseDto;
import com.gianghp.dried_tea_shop.dto.order.OrderResponseDto;
import com.gianghp.dried_tea_shop.dto.order.UpdateOrderStatusRequestDto;
import com.gianghp.dried_tea_shop.entity.CartItem;
import com.gianghp.dried_tea_shop.entity.Order;
import com.gianghp.dried_tea_shop.entity.OrderItem;
import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.entity.User;
import com.gianghp.dried_tea_shop.enums.PaymentMethod;
import com.gianghp.dried_tea_shop.exception.NotFoundException;
import com.gianghp.dried_tea_shop.mapper.OrderMapper;
import com.gianghp.dried_tea_shop.repository.CartItemRepository;
import com.gianghp.dried_tea_shop.repository.OrderItemRepository;
import com.gianghp.dried_tea_shop.repository.OrderRepository;
import com.gianghp.dried_tea_shop.repository.ProductRepository;
import com.gianghp.dried_tea_shop.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class OrderService {

    private final OrderRepository orderRepository;
    private final OrderItemRepository orderItemRepository;
    private final OrderMapper orderMapper;
    private final UserRepository userRepository;
    private final ProductRepository productRepository;
    private final CartItemRepository cartItemRepository;

    public Page<OrderResponseDto> getOrdersByUserId(UUID userId, Pageable pageable) {
        return orderRepository.findByUserId(userId, pageable)
            .map(this::mapToOrderResponseDto);
    }

    public Page<OrderResponseDto> getAllOrders(Pageable pageable) {
        return orderRepository.findAll(pageable)
            .map(this::mapToOrderResponseDto);
    }

    public OrderResponseDto getOrderById(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new RuntimeException("Order not found"));
        return mapToOrderResponseDto(order);
    }

    public OrderResponseDto createOrder(UUID userId, CreateOrderRequestDto createOrderRequestDto) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new NotFoundException(ErrorCode.USER_NOT_FOUND));

        Order order = orderMapper.toEntity(createOrderRequestDto);
        order.setUser(user);

        // Calculate total amount
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (var itemDto : createOrderRequestDto.getItems()) {
            Product product = productRepository.findById(UUID.fromString(itemDto.getProductId()))
                .orElseThrow(() -> new RuntimeException("Product not found"));
            BigDecimal itemTotal = product.getPrice().multiply(BigDecimal.valueOf(itemDto.getQuantity()));
            totalAmount = totalAmount.add(itemTotal);
        }
        order.setTotalAmount(totalAmount);

        Order savedOrder = orderRepository.save(order);

        // Create order items
        for (var itemDto : createOrderRequestDto.getItems()) {
            Product product = productRepository.findById(UUID.fromString(itemDto.getProductId()))
                .orElseThrow(() -> new RuntimeException("Product not found"));

            OrderItem orderItem = orderMapper.toOrderItemEntity(itemDto);
            orderItem.setOrder(savedOrder);
            orderItem.setProduct(product);
            orderItem.setUnitPrice(product.getPrice());

            orderItemRepository.save(orderItem);
        }

        return mapToOrderResponseDto(savedOrder);
    }

    public OrderResponseDto createOrderFromCart(UUID userId, PaymentMethod paymentMethod) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new NotFoundException(ErrorCode.USER_NOT_FOUND));

        // Get cart items
        List<CartItem> cartItems = cartItemRepository.findByUserId(userId);
        if (cartItems.isEmpty()) {
            throw new RuntimeException("Cart is empty");
        }

        // Calculate total amount
        BigDecimal totalAmount = cartItems.stream()
            .map(cartItem -> cartItem.getProduct().getPrice().multiply(BigDecimal.valueOf(cartItem.getQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Create order
        Order order = new Order();
        order.setUser(user);
        order.setTotalAmount(totalAmount);
        order.setPaymentMethod(paymentMethod);

        Order savedOrder = orderRepository.save(order);

        // Create order items from cart items
        for (CartItem cartItem : cartItems) {
            OrderItem orderItem = new OrderItem();
            orderItem.setOrder(savedOrder);
            orderItem.setProduct(cartItem.getProduct());
            orderItem.setQuantity(cartItem.getQuantity());
            orderItem.setUnitPrice(cartItem.getProduct().getPrice());

            orderItemRepository.save(orderItem);
        }

        // Clear cart after creating order
        cartItemRepository.deleteByUserId(userId);

        return mapToOrderResponseDto(savedOrder);
    }

    public OrderResponseDto updateOrderStatus(UUID orderId, UpdateOrderStatusRequestDto updateOrderStatusRequestDto) {
        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new RuntimeException("Order not found"));

        orderMapper.updateEntityFromDto(updateOrderStatusRequestDto, order);
        Order savedOrder = orderRepository.save(order);

        return mapToOrderResponseDto(savedOrder);
    }

    private OrderResponseDto mapToOrderResponseDto(Order order) {
        OrderResponseDto orderResponse = orderMapper.toResponseDto(order);

        // Get order items
        List<OrderItem> orderItems = orderItemRepository.findByOrderId(order.getId());
        List<OrderItemResponseDto> orderItemDtos = orderItems.stream()
            .map(orderMapper::toOrderItemResponseDto)
            .toList();

        orderResponse.setItems(orderItemDtos);
        return orderResponse;
    }
}
