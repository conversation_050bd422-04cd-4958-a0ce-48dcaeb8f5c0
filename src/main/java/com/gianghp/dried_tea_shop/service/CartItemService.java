package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.dto.cart.AddCartItemRequestDto;
import com.gianghp.dried_tea_shop.dto.cart.AddCartItemResponseDto;
import com.gianghp.dried_tea_shop.dto.cart.CartItemDto;
import com.gianghp.dried_tea_shop.dto.cart.CartResponseDto;
import com.gianghp.dried_tea_shop.dto.cart.UpdateCartItemRequestDto;
import com.gianghp.dried_tea_shop.entity.CartItem;
import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.entity.User;
import com.gianghp.dried_tea_shop.exception.NotFoundException;
import com.gianghp.dried_tea_shop.mapper.CartMapper;
import com.gianghp.dried_tea_shop.repository.CartItemRepository;
import com.gianghp.dried_tea_shop.repository.ProductRepository;
import com.gianghp.dried_tea_shop.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CartItemService {

    private final CartItemRepository cartItemRepository;
    private final ProductRepository productRepository;
    private final UserRepository userRepository;
    private final CartMapper cartMapper;

    public CartResponseDto getCartByUserId(UUID userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new NotFoundException(ErrorCode.USER_NOT_FOUND));

        CartResponseDto cartResponse = cartMapper.toResponseDto(user);

        // Get cart items
        List<CartItem> cartItems = cartItemRepository.findByUserId(userId);
        List<CartItemDto> cartItemDtos = cartItems.stream()
            .map(cartMapper::toCartItemDto)
            .toList();

        cartResponse.setItems(cartItemDtos);
        return cartResponse;
    }

    public AddCartItemResponseDto addItemToCart(UUID userId, AddCartItemRequestDto addCartItemRequestDto) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new NotFoundException(ErrorCode.USER_NOT_FOUND));

        Product product = productRepository.findById(addCartItemRequestDto.getProductId())
            .orElseThrow(() -> new NotFoundException(ErrorCode.PRODUCT_NOT_FOUND));

        // Check if item already exists in cart
        CartItem existingCartItem = cartItemRepository.findByUserIdAndProductId(userId, product.getId())
            .orElse(null);

        if (existingCartItem != null) {
            // Update quantity
            existingCartItem.setQuantity(existingCartItem.getQuantity() + addCartItemRequestDto.getQuantity());
            CartItem savedCartItem = cartItemRepository.save(existingCartItem);
            return cartMapper.toAddCartItemResponseDto(savedCartItem);
        } else {
            // Create new cart item
            CartItem cartItem = cartMapper.toEntity(addCartItemRequestDto);
            cartItem.setUser(user);
            cartItem.setProduct(product);

            CartItem savedCartItem = cartItemRepository.save(cartItem);
            return cartMapper.toAddCartItemResponseDto(savedCartItem);
        }
    }

    public AddCartItemResponseDto updateCartItemQuantity(UUID cartItemId, UpdateCartItemRequestDto updateCartItemRequestDto) {
        CartItem cartItem = cartItemRepository.findById(cartItemId)
            .orElseThrow(() -> new NotFoundException("CartItem", cartItemId));

        cartMapper.updateEntityFromDto(updateCartItemRequestDto, cartItem);
        CartItem savedCartItem = cartItemRepository.save(cartItem);

        return cartMapper.toAddCartItemResponseDto(savedCartItem);
    }

    public void removeCartItem(UUID cartItemId) {
        if (!cartItemRepository.existsById(cartItemId)) {
            throw new NotFoundException("CartItem", cartItemId);
        }
        cartItemRepository.deleteById(cartItemId);
    }

    public void clearCart(UUID userId) {
        cartItemRepository.deleteByUserId(userId);
    }
}
