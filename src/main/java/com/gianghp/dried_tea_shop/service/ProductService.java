package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.dto.product.ProductRequestDto;
import com.gianghp.dried_tea_shop.dto.product.ProductResponseDto;
import com.gianghp.dried_tea_shop.entity.Category;
import com.gianghp.dried_tea_shop.entity.Discount;
import com.gianghp.dried_tea_shop.entity.Image;
import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.mapper.ProductMapper;
import com.gianghp.dried_tea_shop.repository.CategoryRepository;
import com.gianghp.dried_tea_shop.repository.DiscountRepository;
import com.gianghp.dried_tea_shop.repository.ImageRepository;
import com.gianghp.dried_tea_shop.repository.ProductRepository;
import com.gianghp.dried_tea_shop.specification.ProductSpecification;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ProductService {
    
    private final ProductRepository productRepository;
    private final ProductMapper productMapper;
    private final CategoryRepository categoryRepository;
    private final DiscountRepository discountRepository;
    private final ImageRepository imageRepository;

    public Page<ProductResponseDto> getAllProducts(
        String name,
        String categoryId,
        String status,
        BigDecimal minPrice,
        BigDecimal maxPrice,
        BigDecimal minRating,
        Integer page,
        Integer size,
        String sortBy
    ) {
        PageRequest pageable = PageRequest.of(page, size, Sort.by(sortBy).descending());

        return productRepository.findAll(
            ProductSpecification.filter(name, categoryId, status, minPrice, maxPrice, minRating),
            pageable
        ).map(productMapper::toResponseDto);
    }

    public ProductResponseDto getProductById(UUID id) {
        return productRepository.findById(id)
            .map(productMapper::toResponseDto)
            .orElseThrow(() -> new RuntimeException("Product not found"));
    }

    public ProductResponseDto createProduct(ProductRequestDto productRequestDto) {
        Product product = productMapper.toEntity(productRequestDto);

        // Set category
        if (productRequestDto.getCategoryId() != null) {
            Category category = categoryRepository.findById(UUID.fromString(productRequestDto.getCategoryId()))
                .orElseThrow(() -> new RuntimeException("Category not found"));
            product.setCategory(category);
        }

        // Set discount if provided
        if (productRequestDto.getDiscountId() != null) {
            Discount discount = discountRepository.findById(UUID.fromString(productRequestDto.getDiscountId()))
                .orElseThrow(() -> new RuntimeException("Discount not found"));
            product.setDiscount(discount);
        }

        // Set image if provided
        if (productRequestDto.getImageId() != null) {
            Image image = imageRepository.findById(UUID.fromString(productRequestDto.getImageId()))
                .orElseThrow(() -> new RuntimeException("Image not found"));
            product.setImage(image);
        }

        Product savedProduct = productRepository.save(product);
        return productMapper.toResponseDto(savedProduct);
    }

    public ProductResponseDto updateProduct(UUID id, ProductRequestDto productRequestDto) {
        Product product = productRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Product not found"));

        productMapper.updateEntityFromDto(productRequestDto, product);

        // Update category
        if (productRequestDto.getCategoryId() != null) {
            Category category = categoryRepository.findById(UUID.fromString(productRequestDto.getCategoryId()))
                .orElseThrow(() -> new RuntimeException("Category not found"));
            product.setCategory(category);
        }

        // Update discount if provided
        if (productRequestDto.getDiscountId() != null) {
            Discount discount = discountRepository.findById(UUID.fromString(productRequestDto.getDiscountId()))
                .orElseThrow(() -> new RuntimeException("Discount not found"));
            product.setDiscount(discount);
        }

        // Update image if provided
        if (productRequestDto.getImageId() != null) {
            Image image = imageRepository.findById(UUID.fromString(productRequestDto.getImageId()))
                .orElseThrow(() -> new RuntimeException("Image not found"));
            product.setImage(image);
        }

        Product savedProduct = productRepository.save(product);
        return productMapper.toResponseDto(savedProduct);
    }
}
