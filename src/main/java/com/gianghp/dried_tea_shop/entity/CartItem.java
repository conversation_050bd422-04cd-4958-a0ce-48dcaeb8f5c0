package com.gianghp.dried_tea_shop.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "cart_item",
       uniqueConstraints = @UniqueConstraint(name = "uq_user_product",
                                           columnNames = {"user_id", "product_id"}))
@Getter
@Setter
public class CartItem extends BaseEntity {

    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", nullable = false)
    private Product product;
}
