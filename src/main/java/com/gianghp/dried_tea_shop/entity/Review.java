package com.gianghp.dried_tea_shop.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "review", 
       uniqueConstraints = @UniqueConstraint(name = "uq_review_user_product", 
                                           columnNames = {"user_id", "product_id"}))
@Getter
@Setter
public class Review extends BaseEntity {
    
    @Column(name = "rating", nullable = false)
    private Integer rating;
    
    @Column(name = "comment")
    private String comment;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", nullable = false)
    private Product product;
}
