package com.gianghp.dried_tea_shop.exception;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import lombok.Getter;

import java.util.Map;

/**
 * Exception cho các lỗi validation
 */
@Getter
public class ValidationException extends RuntimeException {
    
    private final ErrorCode errorCode;
    private final Object data;
    
    public ValidationException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.data = null;
    }
    
    public ValidationException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.data = null;
    }
    
    public ValidationException(ErrorCode errorCode, String message, Object data) {
        super(message);
        this.errorCode = errorCode;
        this.data = data;
    }
    
    public ValidationException(String fieldName, String message) {
        super(String.format("Validation failed for field '%s': %s", fieldName, message));
        this.errorCode = ErrorCode.VALIDATION_ERROR;
        this.data = Map.of("field", fieldName, "error", message);
    }
    
    public ValidationException(Map<String, String> fieldErrors) {
        super("Validation failed for multiple fields");
        this.errorCode = ErrorCode.VALIDATION_ERROR;
        this.data = fieldErrors;
    }
}
