package com.gianghp.dried_tea_shop.exception;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import lombok.Getter;

/**
 * Exception cho các lỗi không tìm thấy resource
 */
@Getter
public class NotFoundException extends RuntimeException {
    
    private final ErrorCode errorCode;
    private final Object data;
    
    public NotFoundException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.data = null;
    }
    
    public NotFoundException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.data = null;
    }
    
    public NotFoundException(ErrorCode errorCode, String message, Object data) {
        super(message);
        this.errorCode = errorCode;
        this.data = data;
    }
    
    public NotFoundException(String resourceName, Object id) {
        super(String.format("%s not found with id: %s", resourceName, id));
        this.errorCode = ErrorCode.RESOURCE_NOT_FOUND;
        this.data = id;
    }
    
    public NotFoundException(String resourceName, String fieldName, Object fieldValue) {
        super(String.format("%s not found with %s: %s", resourceName, fieldName, fieldValue));
        this.errorCode = ErrorCode.RESOURCE_NOT_FOUND;
        this.data = fieldValue;
    }
}
