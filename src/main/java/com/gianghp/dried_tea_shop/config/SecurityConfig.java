package com.gianghp.dried_tea_shop.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
public class SecurityConfig {

  @Bean
  public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
    http
        // Tắt CSRF cho REST API (chỉ bật nếu bạn cần form login)
        .csrf(csrf -> csrf.disable())

        // Cho phép tất cả request, không cần auth
        .authorizeHttpRequests(auth -> auth
            .requestMatchers(
                "/swagger-ui/**",
                "/swagger-ui.html",
                "/v3/api-docs/**",
                "/api-docs/**",
                "/api-docs.yaml"
            ).permitAll()
            .anyRequest().permitAll()
        )

        // Không bật login form, HTTP Basic...
        .formLogin(form -> form.disable())
        .httpBasic(httpBasic -> httpBasic.disable());

    return http.build();
  }
}

