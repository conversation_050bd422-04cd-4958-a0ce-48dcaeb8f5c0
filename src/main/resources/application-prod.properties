spring.application.name=dried-tea-shop

# Port for production
server.port=8080

# Encoding
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# Hide detailed error info (production)
server.error.include-message=never
server.error.include-binding-errors=never

# Database (NeonDB Cloud)
spring.datasource.url=***********************************************************************************************************************************************************************
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Logging (less verbose)
logging.level.root=INFO
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.com.gianghp.dried_tea_shop=INFO

# Validation
spring.mvc.throw-exception-if-no-handler-found=true
spring.web.resources.add-mappings=false

# Swagger (you can disable in prod if needed)
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false
